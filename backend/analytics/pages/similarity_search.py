from typing import Optional, Union

import panel as pn
import param

from server.db import queries, tables


class Page:
    def __init__(self) -> None:
        self.widget_point_id = pn.widgets.TextInput(placeholder="Enter point ID...", sizing_mode="stretch_width")
        self.widget_model_id = pn.widgets.TextInput(placeholder="Enter model ID...", sizing_mode="stretch_width")
        self.search_button = pn.widgets.Button(
            name="Execute Search", button_type="primary", sizing_mode="stretch_width"
        )

        # Sync URL parameters with widget values
        if pn.state.location is not None:
            pn.state.location.sync(self.widget_point_id, {"value": "point_id"})
            pn.state.location.sync(self.widget_model_id, {"value": "model_id"})

        self.search_results = pn.bind(self.search, self.search_button)


    def _get_point(self, point_id: str) -> Optional[Union[tables.LabelPoint, tables.PredictionPoint]]:
        point: Optional[Union[tables.LabelPoint, tables.PredictionPoint]] = None
        point = queries.prediction_points.get(point_id)
        if point is None:
            point = queries.label_points.get(point_id)

        return point

    def _get_model(self, model_id: str) -> Optional[tables.Model]:
        model: Optional[tables.Model] = queries.models.get(model_id)
        return model

    def search(self, compute: bool) -> pn.Column:

        point_id = self.widget_point_id.value.rstrip()
        model_id = self.widget_model_id.value.rstrip()

        print(f"running search", flush=True)
        print(f"Compute: {type(compute)} | \"{compute}\"", flush=True)
        print(f"Point ID: {type(point_id)} | \"{point_id}\"", flush=True)
        print(f"Model ID: {type(model_id)} | \"{model_id}\"", flush=True)

        if point_id == "":
            return pn.Column()
        
        if model_id == "":
            return pn.Column()

        point = self._get_point(point_id)
        model = self._get_model(model_id)

        if point is None:
            return pn.Column(pn.pane.Markdown(f"Point not found: {point_id}"))
        if model is None:
            return pn.Column(pn.pane.Markdown(f"Model not found: {model_id}"))

        return pn.Column(pn.pane.Markdown(f"Search Results"))

    def search_model_by_id(self, model_id: str) -> pn.Column:
        if not model_id.strip():
            return pn.Column()

        model = queries.models.get(model_id)
        if model:
            return pn.Column(
                pn.pane.Markdown(f"**Model:** {model.name} ({model.id})"),
                pn.pane.Markdown(f"Type: {model.type}, Version: {model.version}, Status: {model.status}"),
                sizing_mode="stretch_width",
            )

        return pn.Column(pn.pane.Markdown(f"Model not found: {model_id}"))

    def render(self) -> pn.Column:
        pn.extension(loading_spinner="dots", loading_color="#1e52d6")

        return pn.Column(
            pn.Spacer(max_height=50, sizing_mode="stretch_both"),
            pn.Row(
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                pn.Column(
                    pn.pane.Markdown("# Similarity Search"),
                    pn.pane.Markdown("## Point ID"),
                    pn.Row(self.widget_point_id, self.widget_model_id, self.search_button, sizing_mode="stretch_width"),
                    self.search_results,
                    sizing_mode="stretch_width",
                    min_width=360,
                    align=("center", "start"),
                ),
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                align=("center", "center"),
            ),
        )


def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        page.render(),
        sizing_mode="stretch_width",
        width_policy="max",
    )
